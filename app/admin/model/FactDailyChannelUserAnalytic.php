<?php

declare(strict_types=1);

namespace app\admin\model;

use plugin\admin\app\model\Base;

/**
 * @property string $date 统计日期(主键)
 * @property int $channel_id 渠道
 * @property int $daily_active_users 日活跃用户数
 * @property int $daily_new_users 日新增用户数
 * @property int $total_sessions 总访问次数
 * @property int $total_session_duration_seconds 总停留时长(秒)
 * @property string $avg_session_duration_seconds 平均停留时长(秒)
 * @property string $avg_sessions_per_user 人均访问次数
 * @property int $total_heartbeats 总心跳次数
 * @property string $avg_heartbeats_per_session 平均每会话心跳次数
 * @property int $active_users_count 活跃用户数（排除新增用户）
 * @property int $active_users_sessions 活跃用户总访问次数
 * @property int $active_users_duration_seconds 活跃用户总访问时长（秒）
 * @property string $active_users_avg_session_duration 活跃用户平均停留时长
 * @property string $active_users_avg_sessions_per_user 活跃用户平均访问次数
 * @property int $new_users_sessions 新增用户总访问次数
 * @property int $new_users_duration_seconds 新增用户总访问时长（秒）
 * @property string $new_users_avg_session_duration 新增用户平均停留时长
 * @property string $new_users_avg_sessions_per_user 新增用户平均访问次数
 * @property string $day_1_retention_rate 次日留存率(0-1)
 * @property string $day_3_retention_rate 3日留存率(0-1)
 * @property string $day_7_retention_rate 7日留存率(0-1)
 * @property string $day_30_retention_rate 30日留存率(0-1)
 * @property string $new_users_day_1_retention_rate 新增用户次日留存率(0-1)
 * @property string $new_users_day_3_retention_rate 新增用户3日留存率(0-1)
 * @property string $new_users_day_7_retention_rate 新增用户7日留存率(0-1)
 * @property string $new_users_day_30_retention_rate 新增用户30日留存率(0-1)
 * @property string $active_users_day_1_retention_rate 活跃用户次日留存率(0-1)
 * @property string $active_users_day_3_retention_rate 活跃用户3日留存率(0-1)
 * @property string $active_users_day_7_retention_rate 活跃用户7日留存率(0-1)
 * @property string $active_users_day_30_retention_rate 活跃用户30日留存率(0-1)
 * @property string $created_at 创建时间
 * @property string $updated_at 更新时间
 */
class FactDailyChannelUserAnalytic extends Base
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'fact_daily_channel_user_analytics';

    /**
     * The primary key associated with the table.
     *
     * @var string
     */
    protected $primaryKey = 'date';

    /**
     * Indicates if the model's ID is auto-incrementing.
     *
     * @var bool
     */
    public $incrementing = false;
}
