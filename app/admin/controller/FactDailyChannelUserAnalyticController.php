<?php

declare(strict_types=1);

namespace app\admin\controller;

use app\admin\model\FactDailyChannelUserAnalytic;
use plugin\admin\app\controller\Crud;
use support\Response;

/**
 * 每日渠道用户数据.
 */
class FactDailyChannelUserAnalyticController extends Crud
{
    /**
     * @var FactDailyChannelUserAnalytic
     */
    protected $model = null;

    /**
     * 构造函数.
     *
     * @return void
     */
    public function __construct()
    {
        $this->model = new FactDailyChannelUserAnalytic();
    }

    /**
     * 浏览.
     */
    public function index(): Response
    {
        return view('fact-daily-channel-user-analytic/index');
    }
}
