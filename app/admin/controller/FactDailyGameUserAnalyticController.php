<?php

declare(strict_types=1);

namespace app\admin\controller;

use app\admin\model\FactDailyGameUserAnalytic;
use plugin\admin\app\controller\Crud;
use support\Response;

/**
 * 每日游戏用户数据.
 */
class FactDailyGameUserAnalyticController extends Crud
{
    /**
     * @var FactDailyGameUserAnalytic
     */
    protected $model = null;

    /**
     * 构造函数.
     *
     * @return void
     */
    public function __construct()
    {
        $this->model = new FactDailyGameUserAnalytic();
    }

    /**
     * 浏览.
     */
    public function index(): Response
    {
        return view('fact-daily-game-user-analytic/index');
    }
}
