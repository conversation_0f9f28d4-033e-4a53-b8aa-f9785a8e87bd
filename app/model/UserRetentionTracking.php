<?php

declare(strict_types=1);

namespace app\model;

use Illuminate\Database\Eloquent\Collection;
use support\Model;

/**
 * 用户留存跟踪模型.
 *
 * @property int $user_id 用户ID
 * @property int $channel_id 渠道ID
 * @property int $game_id 游戏ID
 * @property string $cohort_date 用户群组日期(首次活动日期)
 * @property string $activity_date 活动日期
 * @property int $days_since_first_activity 距离首次活动天数
 * @property int $session_count 当日会话数
 * @property int $total_session_duration_seconds 当日总会话时长(秒)
 * @property int $heartbeat_count 当日心跳次数
 * @property string|null $created_at 创建时间
 * @property string|null $updated_at 更新时间
 */
class UserRetentionTracking extends Model
{
    /**
     * 表名.
     */
    protected $table = 'user_retention_tracking';

    /**
     * 主键.
     */
    protected $primaryKey = null;

    /**
     * 是否使用自增主键.
     */
    public $incrementing = false;

    /**
     * 可批量赋值的字段.
     */
    protected $fillable = [
        'user_id',
        'channel_id',
        'game_id',
        'cohort_date',
        'activity_date',
        'days_since_first_activity',
        'session_count',
        'total_session_duration_seconds',
        'heartbeat_count',
    ];

    /**
     * 字段类型转换.
     */
    protected $casts = [
        'user_id' => 'integer',
        'channel_id' => 'integer',
        'game_id' => 'integer',
        'cohort_date' => 'date',
        'activity_date' => 'date',
        'days_since_first_activity' => 'integer',
        'session_count' => 'integer',
        'total_session_duration_seconds' => 'integer',
        'heartbeat_count' => 'integer',
    ];

    /**
     * 格式化日期.
     */
    protected function serializeDate(\DateTimeInterface $date): string
    {
        return $date->format('Y-m-d H:i:s');
    }

    /**
     * 根据用户、渠道、游戏和活动日期查找记录.
     */
    public static function findByUserChannelGameAndDate(int $userId, int $channelId, int $gameId, string $activityDate): ?self
    {
        return self::where('user_id', $userId)
            ->where('channel_id', $channelId)
            ->where('game_id', $gameId)
            ->where('activity_date', $activityDate)
            ->first();
    }

    /**
     * 更新或创建记录.
     */
    public static function updateOrCreateByUserChannelGameAndDate(int $userId, int $channelId, int $gameId, string $activityDate, array $data): self
    {
        return self::updateOrCreate(
            [
                'user_id' => $userId,
                'channel_id' => $channelId,
                'game_id' => $gameId,
                'activity_date' => $activityDate,
            ],
            $data,
        );
    }

    /**
     * 计算留存率.
     */
    public static function calculateRetentionRate(string $cohortDate, int $daysSinceFirst, ?int $channelId = null, ?int $gameId = null): float
    {
        // 获取群组总用户数
        $cohortQuery = UserFirstActivity::where('first_activity_date', $cohortDate);
        if (null !== $channelId) {
            $cohortQuery->where('channel_id', $channelId);
        }
        if (null !== $gameId) {
            $cohortQuery->where('game_id', $gameId);
        }
        $totalCohortUsers = $cohortQuery->count();

        if (0 === $totalCohortUsers) {
            return 0.0;
        }

        // 获取在指定天数后仍活跃的用户数
        $retainedQuery = self::where('cohort_date', $cohortDate)
            ->where('days_since_first_activity', $daysSinceFirst);
        if (null !== $channelId) {
            $retainedQuery->where('channel_id', $channelId);
        }
        if (null !== $gameId) {
            $retainedQuery->where('game_id', $gameId);
        }
        $retainedUsers = $retainedQuery->distinct('user_id')->count();

        return \round($retainedUsers / $totalCohortUsers, 4);
    }

    /**
     * 获取群组留存数据.
     */
    public static function getCohortRetentionData(string $cohortDate, ?int $channelId = null, ?int $gameId = null): array
    {
        $query = self::where('cohort_date', $cohortDate);

        if (null !== $channelId) {
            $query->where('channel_id', $channelId);
        }

        if (null !== $gameId) {
            $query->where('game_id', $gameId);
        }

        return $query->selectRaw('days_since_first_activity, COUNT(DISTINCT user_id) as retained_users')
            ->groupBy('days_since_first_activity')
            ->orderBy('days_since_first_activity')
            ->get()
            ->toArray();
    }

    /**
     * 获取用户活动历史.
     */
    public static function getUserActivityHistory(int $userId, int $channelId, int $gameId): Collection
    {
        return self::where('user_id', $userId)
            ->where('channel_id', $channelId)
            ->where('game_id', $gameId)
            ->orderBy('activity_date')
            ->get();
    }
}
