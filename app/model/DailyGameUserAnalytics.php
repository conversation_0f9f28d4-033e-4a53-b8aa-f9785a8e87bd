<?php

declare(strict_types=1);

namespace app\model;

use Illuminate\Database\Eloquent\Collection;
use support\Model;

/**
 * 每日游戏用户分析统计模型.
 *
 * @property string $date 统计日期
 * @property int $channel_id 渠道ID
 * @property int $game_id 游戏ID
 * @property int $daily_active_users 日活跃用户数
 * @property int $daily_new_users 日新增用户数
 * @property int $total_sessions 总会话数
 * @property int $total_session_duration_seconds 总会话时长(秒)
 * @property float $avg_session_duration_seconds 平均会话时长(秒)
 * @property float $avg_sessions_per_user 人均会话次数
 * @property int $total_heartbeats 总心跳次数
 * @property float $avg_heartbeats_per_session 平均每会话心跳次数
 * @property int $active_users_count 活跃用户数(排除新增用户)
 * @property int $active_users_sessions 活跃用户会话数
 * @property int $active_users_duration_seconds 活跃用户总会话时长(秒)
 * @property float $active_users_avg_session_duration 活跃用户平均会话时长(秒)
 * @property float $active_users_avg_sessions_per_user 活跃用户人均会话次数
 * @property int $new_users_sessions 新增用户会话数
 * @property int $new_users_duration_seconds 新增用户总会话时长(秒)
 * @property float $new_users_avg_session_duration 新增用户平均会话时长(秒)
 * @property float $new_users_avg_sessions_per_user 新增用户人均会话次数
 * @property float|null $day_1_retention_rate 次日留存率(0-1)
 * @property float|null $day_3_retention_rate 3日留存率(0-1)
 * @property float|null $day_7_retention_rate 7日留存率(0-1)
 * @property float|null $day_30_retention_rate 30日留存率(0-1)
 * @property float|null $new_users_day_1_retention_rate 新增用户次日留存率(0-1)
 * @property float|null $new_users_day_3_retention_rate 新增用户3日留存率(0-1)
 * @property float|null $new_users_day_7_retention_rate 新增用户7日留存率(0-1)
 * @property float|null $new_users_day_30_retention_rate 新增用户30日留存率(0-1)
 * @property float|null $active_users_day_1_retention_rate 活跃用户次日留存率(0-1)
 * @property float|null $active_users_day_3_retention_rate 活跃用户3日留存率(0-1)
 * @property float|null $active_users_day_7_retention_rate 活跃用户7日留存率(0-1)
 * @property float|null $active_users_day_30_retention_rate 活跃用户30日留存率(0-1)
 * @property string|null $created_at 创建时间
 * @property string|null $updated_at 更新时间
 */
class DailyGameUserAnalytics extends Model
{
    /**
     * 表名.
     */
    protected $table = 'fact_daily_game_user_analytics';

    /**
     * 主键.
     */
    protected $primaryKey = null;

    /**
     * 是否使用自增主键.
     */
    public $incrementing = false;

    /**
     * 可批量赋值的字段.
     */
    protected $fillable = [
        'date',
        'channel_id',
        'game_id',
        'daily_active_users',
        'daily_new_users',
        'total_sessions',
        'total_session_duration_seconds',
        'avg_session_duration_seconds',
        'avg_sessions_per_user',
        'total_heartbeats',
        'avg_heartbeats_per_session',
        // 活跃用户相关指标
        'active_users_count',
        'active_users_sessions',
        'active_users_duration_seconds',
        'active_users_avg_session_duration',
        'active_users_avg_sessions_per_user',
        // 新增用户相关指标
        'new_users_sessions',
        'new_users_duration_seconds',
        'new_users_avg_session_duration',
        'new_users_avg_sessions_per_user',
        'day_1_retention_rate',
        'day_3_retention_rate',
        'day_7_retention_rate',
        'day_30_retention_rate',
        // 新增用户留存率
        'new_users_day_1_retention_rate',
        'new_users_day_3_retention_rate',
        'new_users_day_7_retention_rate',
        'new_users_day_30_retention_rate',
        // 活跃用户留存率
        'active_users_day_1_retention_rate',
        'active_users_day_3_retention_rate',
        'active_users_day_7_retention_rate',
        'active_users_day_30_retention_rate',
    ];

    /**
     * 字段类型转换.
     */
    protected $casts = [
        'date' => 'date',
        'channel_id' => 'integer',
        'game_id' => 'integer',
        'daily_active_users' => 'integer',
        'daily_new_users' => 'integer',
        'total_sessions' => 'integer',
        'total_session_duration_seconds' => 'integer',
        'avg_session_duration_seconds' => 'decimal:2',
        'avg_sessions_per_user' => 'decimal:2',
        'total_heartbeats' => 'integer',
        'avg_heartbeats_per_session' => 'decimal:2',
        // 活跃用户相关指标
        'active_users_count' => 'integer',
        'active_users_sessions' => 'integer',
        'active_users_duration_seconds' => 'integer',
        'active_users_avg_session_duration' => 'decimal:2',
        'active_users_avg_sessions_per_user' => 'decimal:2',
        // 新增用户相关指标
        'new_users_sessions' => 'integer',
        'new_users_duration_seconds' => 'integer',
        'new_users_avg_session_duration' => 'decimal:2',
        'new_users_avg_sessions_per_user' => 'decimal:2',
        // 总体留存率
        'day_1_retention_rate' => 'decimal:4',
        'day_3_retention_rate' => 'decimal:4',
        'day_7_retention_rate' => 'decimal:4',
        'day_30_retention_rate' => 'decimal:4',
        // 新增用户留存率
        'new_users_day_1_retention_rate' => 'decimal:4',
        'new_users_day_3_retention_rate' => 'decimal:4',
        'new_users_day_7_retention_rate' => 'decimal:4',
        'new_users_day_30_retention_rate' => 'decimal:4',
        // 活跃用户留存率
        'active_users_day_1_retention_rate' => 'decimal:4',
        'active_users_day_3_retention_rate' => 'decimal:4',
        'active_users_day_7_retention_rate' => 'decimal:4',
        'active_users_day_30_retention_rate' => 'decimal:4',
    ];

    /**
     * 格式化日期.
     */
    protected function serializeDate(\DateTimeInterface $date): string
    {
        return $date->format('Y-m-d H:i:s');
    }

    /**
     * 根据日期、渠道和游戏查找记录.
     */
    public static function findByDateChannelAndGame(string $date, int $channelId, int $gameId): ?self
    {
        return self::where('date', $date)
            ->where('channel_id', $channelId)
            ->where('game_id', $gameId)
            ->first();
    }

    /**
     * 更新或创建记录.
     */
    public static function updateOrCreateByDateChannelAndGame(string $date, int $channelId, int $gameId, array $data): self
    {
        return self::updateOrCreate(
            ['date' => $date, 'channel_id' => $channelId, 'game_id' => $gameId],
            $data,
        );
    }

    /**
     * 获取指定日期范围的统计数据.
     */
    public static function getStatsByDateRange(string $startDate, string $endDate, ?int $channelId = null, ?int $gameId = null): Collection
    {
        $query = self::whereBetween('date', [$startDate, $endDate]);

        if (null !== $channelId) {
            $query->where('channel_id', $channelId);
        }

        if (null !== $gameId) {
            $query->where('game_id', $gameId);
        }

        return $query->orderBy('date')
            ->orderBy('channel_id')
            ->orderBy('game_id')
            ->get();
    }

    /**
     * 获取游戏排行榜.
     */
    public static function getGameRanking(string $date, string $metric = 'daily_active_users', ?int $channelId = null): Collection
    {
        $query = self::where('date', $date);

        if (null !== $channelId) {
            $query->where('channel_id', $channelId);
        }

        return $query->orderBy($metric, 'desc')
            ->get();
    }
}
