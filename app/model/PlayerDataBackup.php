<?php

declare(strict_types=1);

namespace app\model;

use Illuminate\Database\Eloquent\Collection;
use support\Model;

/**
 * 玩家数据备份模型.
 *
 * @property int $id 主键ID
 * @property int $user_id 用户ID
 * @property int $channel_id 渠道ID
 * @property int $game_id 游戏ID
 * @property string $backup_key 备份数据键名
 * @property array $backup_data 备份数据内容(JSON格式)
 * @property int $data_version 数据版本号
 * @property int $data_size 数据大小(字节)
 * @property string|null $checksum 数据校验和
 * @property int $backup_type 备份类型: 1=手动备份, 2=自动备份, 3=关键节点备份
 * @property string|null $description 备份描述
 * @property string|null $ip_address 客户端IP地址
 * @property string|null $user_agent 客户端User Agent
 * @property array|null $device_info 设备信息
 * @property int $status 状态: 1=正常, 2=已删除, 3=已损坏
 * @property string $created_at 创建时间
 * @property string $updated_at 更新时间
 */
class PlayerDataBackup extends Model
{
    /**
     * 与模型关联的表名.
     */
    protected $table = 'player_data_backups';

    /**
     * 主键.
     */
    protected $primaryKey = 'id';

    /**
     * 可批量赋值的字段.
     */
    protected $fillable = [
        'user_id',
        'channel_id',
        'game_id',
        'backup_key',
        'backup_data',
        'data_version',
        'data_size',
        'checksum',
        'backup_type',
        'description',
        'ip_address',
        'user_agent',
        'device_info',
        'status',
    ];

    /**
     * 字段类型转换.
     */
    protected $casts = [
        'backup_data' => 'array',
        'device_info' => 'array',
        'user_id' => 'integer',
        'channel_id' => 'integer',
        'game_id' => 'integer',
        'data_version' => 'integer',
        'data_size' => 'integer',
        'backup_type' => 'integer',
        'status' => 'integer',
    ];

    /**
     * 备份类型常量.
     */
    public const BACKUP_TYPE_MANUAL = 1;
    public const BACKUP_TYPE_AUTO = 2;
    public const BACKUP_TYPE_CHECKPOINT = 3;

    /**
     * 状态常量.
     */
    public const STATUS_NORMAL = 1;
    public const STATUS_DELETED = 2;
    public const STATUS_CORRUPTED = 3;

    /**
     * 根据用户ID、渠道ID、游戏ID和备份键查找活跃备份.
     *
     * @param int $userId 用户ID
     * @param int $channelId 渠道ID
     * @param int $gameId 游戏ID
     * @param string $backupKey 备份键
     */
    public static function findActiveBackup(int $userId, int $channelId, int $gameId, string $backupKey): ?self
    {
        return self::where('user_id', $userId)
            ->where('channel_id', $channelId)
            ->where('game_id', $gameId)
            ->where('backup_key', $backupKey)
            ->where('status', self::STATUS_NORMAL)
            ->orderBy('created_at', 'desc')
            ->first();
    }

    /**
     * 获取用户的所有活跃备份.
     *
     * @param int $userId 用户ID
     * @param int $channelId 渠道ID
     * @param int $gameId 游戏ID
     *
     * @return Collection
     */
    public static function getUserActiveBackups(int $userId, int $channelId, int $gameId)
    {
        return self::where('user_id', $userId)
            ->where('channel_id', $channelId)
            ->where('game_id', $gameId)
            ->where('status', self::STATUS_NORMAL)
            ->orderBy('backup_key')
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * 软删除备份（标记为已删除状态）.
     */
    public function softDelete(): bool
    {
        $this->status = self::STATUS_DELETED;

        return $this->save();
    }

    /**
     * 标记为损坏状态.
     */
    public function markAsCorrupted(): bool
    {
        $this->status = self::STATUS_CORRUPTED;

        return $this->save();
    }

    /**
     * 计算数据校验和.
     *
     * @param array $data 数据
     */
    public static function calculateChecksum(array $data): string
    {
        // 递归排序数组键
        $normalizedData = self::recursiveKsort($data);

        return \hash('sha256', \json_encode($normalizedData, \JSON_UNESCAPED_UNICODE | \JSON_UNESCAPED_SLASHES));
    }

    /**
     * 递归排序数组键.
     *
     * @param mixed $data 要排序的数据
     *
     * @return mixed 排序后的数据
     */
    private static function recursiveKsort($data)
    {
        if (!\is_array($data)) {
            return $data;
        }

        // 对当前层级进行键排序
        \ksort($data);

        // 递归处理每个值
        foreach ($data as $key => $value) {
            $data[$key] = self::recursiveKsort($value);
        }

        return $data;
    }

    /**
     * 验证数据完整性.
     */
    public function verifyIntegrity(): bool
    {
        if (!$this->checksum || !$this->backup_data) {
            return false;
        }

        $calculatedChecksum = self::calculateChecksum($this->backup_data);

        return \hash_equals($this->checksum, $calculatedChecksum);
    }

    /**
     * 获取备份类型描述.
     */
    public function getBackupTypeDescription(): string
    {
        return match ($this->backup_type) {
            self::BACKUP_TYPE_MANUAL => '手动备份',
            self::BACKUP_TYPE_AUTO => '自动备份',
            self::BACKUP_TYPE_CHECKPOINT => '关键节点备份',
            default => '未知类型',
        };
    }

    /**
     * 获取状态描述.
     */
    public function getStatusDescription(): string
    {
        return match ($this->status) {
            self::STATUS_NORMAL => '正常',
            self::STATUS_DELETED => '已删除',
            self::STATUS_CORRUPTED => '已损坏',
            default => '未知状态',
        };
    }
}
