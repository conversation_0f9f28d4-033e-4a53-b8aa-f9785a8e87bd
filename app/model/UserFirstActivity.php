<?php

declare(strict_types=1);

namespace app\model;

use Illuminate\Database\Eloquent\Collection;
use support\Model;

/**
 * 用户首次活动记录模型.
 *
 * @property int $user_id 用户ID
 * @property int $channel_id 渠道ID
 * @property int $game_id 游戏ID
 * @property string $first_activity_date 首次活动日期
 * @property string $first_session_id 首次会话ID
 * @property int $first_session_start_time 首次会话开始时间戳(毫秒)
 * @property string|null $registration_date 用户注册日期
 * @property bool $is_new_user 是否为新用户
 * @property string|null $created_at 创建时间
 * @property string|null $updated_at 更新时间
 */
class UserFirstActivity extends Model
{
    /**
     * 表名.
     */
    protected $table = 'user_first_activity';

    /**
     * 主键.
     */
    protected $primaryKey = null;

    /**
     * 是否使用自增主键.
     */
    public $incrementing = false;

    /**
     * 可批量赋值的字段.
     */
    protected $fillable = [
        'user_id',
        'channel_id',
        'game_id',
        'first_activity_date',
        'first_session_id',
        'first_session_start_time',
        'registration_date',
        'is_new_user',
    ];

    /**
     * 字段类型转换.
     */
    protected $casts = [
        'user_id' => 'integer',
        'channel_id' => 'integer',
        'game_id' => 'integer',
        'first_activity_date' => 'date',
        'first_session_start_time' => 'integer',
        'registration_date' => 'date',
        'is_new_user' => 'boolean',
    ];

    /**
     * 格式化日期.
     */
    protected function serializeDate(\DateTimeInterface $date): string
    {
        return $date->format('Y-m-d H:i:s');
    }

    /**
     * 根据用户、渠道和游戏查找记录.
     */
    public static function findByUserChannelAndGame(int $userId, int $channelId, int $gameId): ?self
    {
        return self::where('user_id', $userId)
            ->where('channel_id', $channelId)
            ->where('game_id', $gameId)
            ->first();
    }

    /**
     * 更新或创建记录.
     */
    public static function updateOrCreateByUserChannelAndGame(int $userId, int $channelId, int $gameId, array $data): self
    {
        return self::updateOrCreate(
            ['user_id' => $userId, 'channel_id' => $channelId, 'game_id' => $gameId],
            $data,
        );
    }

    /**
     * 获取指定日期的新用户数量.
     */
    public static function getNewUserCountByDate(string $date, ?int $channelId = null, ?int $gameId = null): int
    {
        $query = self::where('first_activity_date', $date)
            ->where('is_new_user', true);

        if (null !== $channelId) {
            $query->where('channel_id', $channelId);
        }

        if (null !== $gameId) {
            $query->where('game_id', $gameId);
        }

        return $query->count();
    }

    /**
     * 获取指定日期范围的新用户列表.
     */
    public static function getNewUsersByDateRange(string $startDate, string $endDate, ?int $channelId = null, ?int $gameId = null): Collection
    {
        $query = self::whereBetween('first_activity_date', [$startDate, $endDate])
            ->where('is_new_user', true);

        if (null !== $channelId) {
            $query->where('channel_id', $channelId);
        }

        if (null !== $gameId) {
            $query->where('game_id', $gameId);
        }

        return $query->orderBy('first_activity_date')
            ->get();
    }

    /**
     * 获取用户群组(按首次活动日期分组).
     */
    public static function getUserCohortsByDate(string $cohortDate, ?int $channelId = null, ?int $gameId = null): Collection
    {
        $query = self::where('first_activity_date', $cohortDate);

        if (null !== $channelId) {
            $query->where('channel_id', $channelId);
        }

        if (null !== $gameId) {
            $query->where('game_id', $gameId);
        }

        return $query->get();
    }
}
