<?php

declare(strict_types=1);

namespace app\strategy\unpack;

use support\Log;

trait AtomicUnpackTrait
{
    /**
     * 原子化解压操作.
     */
    protected function atomicUnpack(string $sourcePath, string $targetDirectory, callable $unpackFunction): bool
    {
        // 创建临时目录
        $tempDirectory = $this->createTempDirectory($targetDirectory);
        if (!$tempDirectory) {
            return false;
        }

        try {
            // 在临时目录中执行解压
            $result = $unpackFunction($sourcePath, $tempDirectory);

            if ($result) {
                // 解压成功，将内容移动到目标目录
                if ($this->moveDirectoryContents($tempDirectory, $targetDirectory)) {
                    Log::info('Atomic unpack completed successfully');

                    return true;
                }
                Log::error('Failed to move contents from temp directory');

                return false;
            }
            Log::error('Unpack operation failed in temp directory');

            return false;
        } finally {
            // 清理临时目录
            $this->cleanupTempDirectory($tempDirectory);
        }
    }

    /**
     * 创建临时目录.
     */
    private function createTempDirectory(string $baseDirectory): ?string
    {
        $tempDir = \dirname($baseDirectory) . \DIRECTORY_SEPARATOR . 'temp_' . \uniqid('unpack_', true);

        if (!\mkdir($tempDir, 0755, true)) {
            Log::error("Failed to create temp directory: {$tempDir}");

            return null;
        }

        Log::info("Created temp directory: {$tempDir}");

        return $tempDir;
    }

    /**
     * 移动目录内容，完全覆盖目标目录原有文件.
     */
    private function moveDirectoryContents(string $sourceDir, string $targetDir): bool
    {
        try {
            // 首先清空目标目录（如果存在）
            if (\is_dir($targetDir)) {
                if (!$this->removeDirectoryContents($targetDir)) {
                    Log::error("Failed to clear target directory: {$targetDir}");

                    return false;
                }
            } elseif (!\mkdir($targetDir, 0755, true)) {
                // 创建目标目录
                Log::error("Failed to create target directory: {$targetDir}");

                return false;
            }

            $iterator = new \RecursiveIteratorIterator(
                new \RecursiveDirectoryIterator($sourceDir, \RecursiveDirectoryIterator::SKIP_DOTS),
                \RecursiveIteratorIterator::SELF_FIRST,
            );

            foreach ($iterator as $item) {
                $relativePath = $iterator->getSubPathName();
                $targetPath = $targetDir . \DIRECTORY_SEPARATOR . $relativePath;

                if ($item->isDir()) {
                    if (!\is_dir($targetPath) && !\mkdir($targetPath, 0755, true)) {
                        Log::error("Failed to create directory: {$targetPath}");

                        return false;
                    }
                } else {
                    $targetFileDir = \dirname($targetPath);
                    if (!\is_dir($targetFileDir) && !\mkdir($targetFileDir, 0755, true)) {
                        Log::error("Failed to create directory for file: {$targetFileDir}");

                        return false;
                    }

                    // 如果目标文件存在，先删除它以确保完全覆盖
                    if (\file_exists($targetPath) && !\unlink($targetPath)) {
                        Log::error("Failed to remove existing file: {$targetPath}");

                        return false;
                    }

                    if (!\rename($item->getRealPath(), $targetPath)) {
                        Log::error("Failed to move file from {$item->getRealPath()} to {$targetPath}");

                        return false;
                    }
                }
            }

            return true;
        } catch (\Exception $e) {
            Log::error('Error moving directory contents: ' . $e->getMessage());

            return false;
        }
    }

    /**
     * 清空目录内容但保留目录本身.
     */
    private function removeDirectoryContents(string $directory): bool
    {
        try {
            $files = new \RecursiveIteratorIterator(
                new \RecursiveDirectoryIterator($directory, \RecursiveDirectoryIterator::SKIP_DOTS),
                \RecursiveIteratorIterator::CHILD_FIRST,
            );

            foreach ($files as $fileinfo) {
                if ($fileinfo->isDir()) {
                    if (!\rmdir($fileinfo->getRealPath())) {
                        Log::error("Failed to remove directory: {$fileinfo->getRealPath()}");

                        return false;
                    }
                } elseif (!\unlink($fileinfo->getRealPath())) {
                    Log::error("Failed to remove file: {$fileinfo->getRealPath()}");

                    return false;
                }
            }

            return true;
        } catch (\Exception $e) {
            Log::error('Error removing directory contents: ' . $e->getMessage());

            return false;
        }
    }

    /**
     * 清理临时目录.
     */
    private function cleanupTempDirectory(string $tempDirectory): void
    {
        try {
            if (\is_dir($tempDirectory)) {
                $files = new \RecursiveIteratorIterator(
                    new \RecursiveDirectoryIterator($tempDirectory, \RecursiveDirectoryIterator::SKIP_DOTS),
                    \RecursiveIteratorIterator::CHILD_FIRST,
                );

                foreach ($files as $fileinfo) {
                    if ($fileinfo->isDir()) {
                        \rmdir($fileinfo->getRealPath());
                    } else {
                        \unlink($fileinfo->getRealPath());
                    }
                }

                \rmdir($tempDirectory);
                Log::info("Cleaned up temp directory: {$tempDirectory}");
            }
        } catch (\Exception $e) {
            Log::error("Failed to cleanup temp directory {$tempDirectory}: " . $e->getMessage());
        }
    }
}
