<?php

declare(strict_types=1);

namespace app\api\controller;

use app\admin\model\Game;
use app\controller\BaseTraitController;
use app\dto\PlayerDataBackupRequest;
use app\dto\PlayerDataRetrievalRequest;
use app\service\GameService;
use app\service\PlayerDataBackupService;
use DI\Attribute\Inject;
use support\exception\BusinessException;
use support\Log;
use support\Request;
use support\Response;
use Webman\RateLimiter\Annotation\RateLimiter;

/**
 * 玩家数据备份控制器.
 *
 * 提供玩家数据备份和检索的RESTful API接口
 * 包括数据备份、数据检索等功能
 */
class PlayerDataBackupController
{
    use BaseTraitController;

    /**
     * 不需要登录验证的方法.
     */
    protected array $noNeedLogin = [];

    #[Inject]
    protected PlayerDataBackupService $backupService;

    #[Inject]
    protected GameService $gameService;

    /**
     * 备份玩家数据.
     *
     * POST /api/player-data/backup
     */
    #[RateLimiter(ttl: 60, limit: 30)] // 每分钟最多30次备份请求
    public function backup(Request $request): Response
    {
        try {
            $currentUser = $this->getCurrentUser();
            $userId = $currentUser['id'];
            $channelId = $currentUser['channel_id'];

            // 验证和解析请求数据
            $backupRequest = PlayerDataBackupRequest::fromRequest($request);
            $game = $this->gameService->getGameByCode($backupRequest->gameId);
            if (!$game instanceof Game) {
                return $this->errorResponse('Game not found');
            }
            $backupRequest->gameId = $game->id;

            // 获取客户端信息
            $ipAddress = $request->getRealIp();
            $userAgent = $request->header('User-Agent');

            // 执行备份操作
            $backup = $this->backupService->backupPlayerData(
                $userId,
                $channelId,
                $backupRequest,
                $ipAddress,
                $userAgent,
            );

            // 返回成功响应
            return $this->successResponse(0, 'Backup created successfully', [
                'backup_id' => $backup->id,
                'backup_key' => $backup->backup_key,
                'data_size' => $backup->data_size,
                'backup_type' => $backup->backup_type,
                'backup_type_description' => $backup->getBackupTypeDescription(),
                'created_at' => $backup->created_at,
                'checksum' => $backup->checksum,
            ]);
        } catch (BusinessException $e) {
            Log::warning('Player data backup failed', [
                'user_id' => $currentUser['id'] ?? null,
                'error' => $e->getMessage(),
                'request_data' => $request->all(),
            ]);

            return $this->errorResponse($e->getMessage());
        } catch (\Throwable $e) {
            Log::error('Player data backup error', [
                'user_id' => $currentUser['id'] ?? null,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return $this->errorResponse('Internal server error');
        }
    }

    /**
     * 检索玩家数据.
     *
     * GET /api/player-data/retrieve
     * GET /api/player-data/retrieve/{backup_key}
     */
    #[RateLimiter(ttl: 60, limit: 60)] // 每分钟最多60次检索请求
    public function retrieve(Request $request): Response
    {
        try {
            $currentUser = $this->getCurrentUser();
            $userId = $currentUser['id'];
            $channelId = $currentUser['channel_id'];

            // 验证和解析请求数据
            $retrievalRequest = PlayerDataRetrievalRequest::fromRequest($request);
            $game = $this->gameService->getGameByCode($retrievalRequest->gameId);
            if (!$game instanceof Game) {
                return $this->errorResponse('Game not found');
            }
            $retrievalRequest->gameId = $game->id;

            // 执行检索操作
            $backupData = $this->backupService->retrievePlayerData(
                $userId,
                $channelId,
                $retrievalRequest,
            );

            // 返回成功响应
            if ($retrievalRequest->isSpecificBackupKey()) {
                // 返回单个备份数据
                return $this->successResponse(0, 'Backup data retrieved successfully', $backupData);
            }

            // 返回所有备份数据
            return $this->successResponse(0, 'All backup data retrieved successfully', [
                'backups' => $backupData,
                'total_count' => \count($backupData),
            ]);
        } catch (BusinessException $e) {
            Log::warning('Player data retrieval failed', [
                'user_id' => $currentUser['id'] ?? null,
                'error' => $e->getMessage(),
                'request_data' => $request->all(),
            ]);

            return $this->errorResponse($e->getMessage());
        } catch (\Throwable $e) {
            Log::error('Player data retrieval error', [
                'user_id' => $currentUser['id'] ?? null,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return $this->errorResponse('Internal server error');
        }
    }

    /**
     * 获取用户备份统计信息.
     *
     * GET /api/player-data/stats
     */
    #[RateLimiter(ttl: 60, limit: 30)] // 每分钟最多30次统计请求
    public function stats(Request $request): Response
    {
        try {
            $currentUser = $this->getCurrentUser();
            $userId = $currentUser['id'];
            $channelId = $currentUser['channel_id'];

            $gameId = $request->input('game_id');
            if (!$gameId || !\is_numeric($gameId) || $gameId <= 0 || 'undefined' === $gameId) {
                return $this->errorResponse('Invalid game_id parameter');
            }
            $game = $this->gameService->getGameByCode((int) $gameId);
            if (!$game instanceof Game) {
                return $this->errorResponse('Game not found');
            }
            $gameId = $game->id;

            // 获取用户的所有备份数据（用于统计）
            $retrievalRequest = new PlayerDataRetrievalRequest(['game_id' => (int) $gameId]);
            $backups = $this->backupService->retrievePlayerData($userId, $channelId, $retrievalRequest);

            // 计算统计信息
            $totalBackups = \count($backups);
            $totalDataSize = \array_sum(\array_column($backups, 'data_size'));
            $backupTypes = \array_count_values(\array_column($backups, 'backup_type'));

            return $this->successResponse(0, 'Backup statistics retrieved successfully', [
                'total_backups' => $totalBackups,
                'total_data_size' => $totalDataSize,
                'backup_types' => $backupTypes,
                'latest_backup' => $totalBackups > 0 ? $backups[0]['created_at'] : null,
            ]);
        } catch (BusinessException $e) {
            return $this->errorResponse($e->getMessage());
        } catch (\Throwable $e) {
            Log::error('Player data stats error', [
                'user_id' => $currentUser['id'] ?? null,
                'error' => $e->getMessage(),
            ]);

            return $this->errorResponse('Internal server error');
        }
    }
}
